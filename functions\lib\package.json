{"name": "functions", "version": "1.0.0", "main": "lib/index.js", "scripts": {"build": "tsc -p tsconfig.json", "serve": "npm run build && firebase emulators:start --only functions,firestore,auth", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log", "format": "prettier --write \"src/**/*.{ts,js,json,md}\"", "lint": "eslint \"src/**/*.ts\"", "lint:fix": "eslint \"src/**/*.ts\" --fix", "test": "jest --config jest.config.js", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "npm run build && firebase emulators:exec --only auth,firestore,functions --project sigma-nova \"jest --config jest.config.js --runInBand\""}, "engines": {"node": "18"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"firebase-admin": "^13.4.0", "firebase-functions": "^6.4.0", "pdf-lib": "^1.17.1"}, "devDependencies": {"@firebase/rules-unit-testing": "^3.0.4", "@types/jest": "^29.5.12", "@typescript-eslint/eslint-plugin": "^8.39.0", "@typescript-eslint/parser": "^8.39.0", "eslint": "^8.57.1", "eslint-config-google": "^0.14.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-import": "^2.25.4", "eslint-plugin-prettier": "^5.5.3", "firebase-functions-test": "^3.1.0", "firebase-tools": "^13.0.0", "jest": "^29.7.0", "prettier": "^3.6.2", "ts-jest": "^29.2.5", "typescript": "^5.9.2"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "testTimeout": 30000, "setupFilesAfterEnv": ["<rootDir>/src/tests/setup.ts"]}}