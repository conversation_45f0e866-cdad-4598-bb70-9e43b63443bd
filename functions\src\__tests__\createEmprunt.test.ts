import * as admin from "firebase-admin";
import * as functions from "firebase-functions/v1";
import { createEmprunt } from "../emprunts/createEmprunt";

// Mock Firebase Admin
jest.mock("firebase-admin", () => ({
  firestore: jest.fn(() => ({
    runTransaction: jest.fn(),
    collection: jest.fn(),
  })),
  FieldValue: {
    serverTimestamp: jest.fn(() => "TIMESTAMP"),
  },
  Timestamp: {
    fromDate: jest.fn((date) => ({ toDate: () => date })),
  },
}));

// Mock Firebase Functions
jest.mock("firebase-functions/v1", () => ({
  https: {
    HttpsError: class HttpsError extends Error {
      constructor(public code: string, public message: string) {
        super(message);
        this.name = "HttpsError";
      }
    },
    onCall: jest.fn((handler) => handler),
  },
  logger: {
    info: jest.fn(),
    error: jest.fn(),
  },
}));

// Mock auth utility
jest.mock("../utils/auth", () => ({
  checkRegisseurOrAdmin: jest.fn(),
}));

// Mock validation utility
jest.mock("../utils/validation", () => ({
  validateEmpruntData: jest.fn(),
}));

const mockDb = {
  runTransaction: jest.fn(),
  collection: jest.fn(),
};

const mockCheckRegisseurOrAdmin = require("../utils/auth").checkRegisseurOrAdmin;
const mockValidateEmpruntData = require("../utils/validation").validateEmpruntData;

describe("createEmprunt Cloud Function", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (admin.firestore as jest.Mock).mockReturnValue(mockDb);
  });

  describe("Succès", () => {
    it("devrait créer un emprunt avec succès", async () => {
      // Arrange
      const mockData = {
        nom: "Test Manipulation",
        lieu: "Salle de test",
        dateDepart: new Date("2024-12-01"),
        dateRetourPrevue: new Date("2024-12-05"),
        secteur: "Test Secteur",
        referent: "Test Référent",
        emprunteur: "Test Emprunteur",
        materiel: [
          { type: "module", idMateriel: "module1", quantite: 1 },
        ],
      };

      const mockContext = {
        auth: { uid: "test-user-id" },
      };

      const mockEmpruntRef = {
        id: "test-emprunt-id",
        collection: jest.fn(() => ({
          doc: jest.fn(() => ({ set: jest.fn() })),
        })),
      };

      mockValidateEmpruntData.mockReturnValue(mockData);
      mockDb.collection.mockReturnValue({
        doc: jest.fn(() => mockEmpruntRef),
      });
      mockDb.runTransaction.mockImplementation(async (callback) => {
        const mockTransaction = {
          set: jest.fn(),
        };
        return await callback(mockTransaction);
      });

      // Act
      const result = await createEmprunt(mockData, mockContext);

      // Assert
      expect(mockCheckRegisseurOrAdmin).toHaveBeenCalledWith(mockContext);
      expect(mockValidateEmpruntData).toHaveBeenCalledWith(mockData);
      expect(mockDb.runTransaction).toHaveBeenCalled();
      expect(result).toEqual({
        success: true,
        empruntId: "test-emprunt-id",
      });
    });
  });

  describe("Erreurs de permission", () => {
    it("devrait rejeter si l'utilisateur n'est pas régisseur ou admin", async () => {
      // Arrange
      const mockData = { nom: "Test" };
      const mockContext = { auth: { uid: "test-user" } };

      mockCheckRegisseurOrAdmin.mockImplementation(() => {
        throw new functions.https.HttpsError(
          "permission-denied",
          "Accès refusé",
        );
      });

      // Act & Assert
      await expect(createEmprunt(mockData, mockContext)).rejects.toThrow(
        "Accès refusé",
      );
      expect(mockCheckRegisseurOrAdmin).toHaveBeenCalledWith(mockContext);
    });

    it("devrait rejeter si l'utilisateur n'est pas authentifié", async () => {
      // Arrange
      const mockData = { nom: "Test" };
      const mockContext = { auth: null };

      mockCheckRegisseurOrAdmin.mockImplementation(() => {
        throw new functions.https.HttpsError(
          "unauthenticated",
          "Utilisateur non authentifié",
        );
      });

      // Act & Assert
      await expect(createEmprunt(mockData, mockContext)).rejects.toThrow(
        "Utilisateur non authentifié",
      );
    });
  });

  describe("Erreurs de validation", () => {
    it("devrait rejeter si les données sont invalides", async () => {
      // Arrange
      const mockData = { nom: "" }; // Nom vide
      const mockContext = { auth: { uid: "test-user" } };

      mockValidateEmpruntData.mockImplementation(() => {
        throw new functions.https.HttpsError(
          "invalid-argument",
          "Nom requis",
        );
      });

      // Act & Assert
      await expect(createEmprunt(mockData, mockContext)).rejects.toThrow(
        "Nom requis",
      );
      expect(mockValidateEmpruntData).toHaveBeenCalledWith(mockData);
    });

    it("devrait rejeter si les dates sont incohérentes", async () => {
      // Arrange
      const mockData = {
        nom: "Test",
        dateDepart: new Date("2024-12-05"),
        dateRetourPrevue: new Date("2024-12-01"), // Date antérieure
      };
      const mockContext = { auth: { uid: "test-user" } };

      mockValidateEmpruntData.mockImplementation(() => {
        throw new functions.https.HttpsError(
          "invalid-argument",
          "La date de retour doit être postérieure à la date de départ",
        );
      });

      // Act & Assert
      await expect(createEmprunt(mockData, mockContext)).rejects.toThrow(
        "La date de retour doit être postérieure à la date de départ",
      );
    });
  });

  describe("Cas limites", () => {
    it("devrait gérer un emprunt sans matériel", async () => {
      // Arrange
      const mockData = {
        nom: "Test sans matériel",
        lieu: "Salle de test",
        dateDepart: new Date("2024-12-01"),
        dateRetourPrevue: new Date("2024-12-05"),
        secteur: "Test",
        referent: "Test",
        emprunteur: "Test",
        materiel: [], // Pas de matériel
      };
      const mockContext = { auth: { uid: "test-user" } };

      mockValidateEmpruntData.mockReturnValue(mockData);
      const mockEmpruntRef = { id: "test-id", collection: jest.fn() };
      mockDb.collection.mockReturnValue({
        doc: jest.fn(() => mockEmpruntRef),
      });
      mockDb.runTransaction.mockImplementation(async (callback) => {
        return await callback({ set: jest.fn() });
      });

      // Act
      const result = await createEmprunt(mockData, mockContext);

      // Assert
      expect(result.success).toBe(true);
    });

    it("devrait gérer les erreurs de transaction Firestore", async () => {
      // Arrange
      const mockData = { nom: "Test" };
      const mockContext = { auth: { uid: "test-user" } };

      mockValidateEmpruntData.mockReturnValue(mockData);
      mockDb.runTransaction.mockRejectedValue(new Error("Transaction failed"));

      // Act & Assert
      await expect(createEmprunt(mockData, mockContext)).rejects.toThrow(
        "Erreur interne lors de la création de l'emprunt",
      );
    });
  });
});
