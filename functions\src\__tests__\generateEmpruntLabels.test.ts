import * as admin from "firebase-admin";
import * as functions from "firebase-functions/v1";
import { generateEmpruntLabels } from "../emprunts/generateEmpruntLabels";

// Mock Firebase Admin
jest.mock("firebase-admin", () => ({
  firestore: jest.fn(() => ({
    collection: jest.fn(),
    getAll: jest.fn(),
  })),
  Timestamp: {
    fromDate: jest.fn((date) => ({ toDate: () => date })),
  },
}));

// Mock Firebase Functions
jest.mock("firebase-functions/v1", () => ({
  https: {
    HttpsError: class HttpsError extends Error {
      constructor(public code: string, public message: string) {
        super(message);
        this.name = "HttpsError";
      }
    },
    onCall: jest.fn((handler) => handler),
  },
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
  },
}));

// Mock pdf-lib
jest.mock("pdf-lib", () => ({
  PDFDocument: {
    create: jest.fn(() => ({
      addPage: jest.fn(() => ({
        drawText: jest.fn(),
        drawRectangle: jest.fn(),
        getSize: jest.fn(() => ({ width: 595, height: 842 })),
      })),
      embedFont: jest.fn(),
      save: jest.fn(() => Promise.resolve(new Uint8Array([1, 2, 3]))),
    })),
  },
  rgb: jest.fn(),
  StandardFonts: {
    Helvetica: "Helvetica",
    HelveticaBold: "HelveticaBold",
  },
}));

// Mock auth utility
jest.mock("../utils/auth", () => ({
  checkRegisseurOrAdmin: jest.fn(),
}));

const mockDb = {
  collection: jest.fn(),
  getAll: jest.fn(),
};

const mockCheckRegisseurOrAdmin = require("../utils/auth").checkRegisseurOrAdmin;

describe("generateEmpruntLabels Cloud Function", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (admin.firestore as jest.Mock).mockReturnValue(mockDb);
    // Mock Date.now pour les tests de performance
    jest.spyOn(Date, "now").mockReturnValue(1000);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe("Génération PDF réussie", () => {
    it("devrait générer un PDF avec succès", async () => {
      // Arrange
      const mockData = { empruntId: "test-emprunt-id" };
      const mockContext = { auth: { uid: "test-user-id" } };

      const mockEmpruntDoc = {
        exists: true,
        data: () => ({
          nom: "Test Manipulation",
          lieu: "Salle de test",
          dateDepart: { toDate: () => new Date("2024-12-01") },
          dateRetourPrevue: { toDate: () => new Date("2024-12-05") },
          referent: "Test Référent",
          emprunteur: "Test Emprunteur",
        }),
      };

      const mockMaterielSnapshot = {
        docs: [
          {
            data: () => ({
              type: "module",
              idMateriel: "module1",
              quantite: 1,
            }),
          },
        ],
      };

      const mockModuleDoc = {
        exists: true,
        data: () => ({ nom: "Module Test" }),
      };

      const mockEmpruntRef = {
        get: jest.fn().mockResolvedValue(mockEmpruntDoc),
        collection: jest.fn(() => ({
          get: jest.fn().mockResolvedValue(mockMaterielSnapshot),
        })),
      };

      mockDb.collection.mockReturnValue({
        doc: jest.fn(() => mockEmpruntRef),
      });
      mockDb.getAll.mockResolvedValue([mockModuleDoc]);

      // Mock performance timing
      let callCount = 0;
      jest.spyOn(Date, "now").mockImplementation(() => {
        callCount++;
        return callCount === 1 ? 1000 : 2500; // 1.5 secondes
      });

      // Act
      const result = await generateEmpruntLabels(mockData, mockContext);

      // Assert
      expect(mockCheckRegisseurOrAdmin).toHaveBeenCalledWith(mockContext);
      expect(result.success).toBe(true);
      expect(result.pdf).toBeDefined();
      expect(result.filename).toBe("etiquettes_emprunt_test-emprunt-id.pdf");
      expect(result.duration).toBe(1500);
    });

    it("devrait gérer un emprunt avec stocks et modules", async () => {
      // Arrange
      const mockData = { empruntId: "test-emprunt-id" };
      const mockContext = { auth: { uid: "test-user-id" } };

      const mockEmpruntDoc = {
        exists: true,
        data: () => ({
          nom: "Test Manipulation",
          lieu: "Salle de test",
          dateDepart: { toDate: () => new Date("2024-12-01") },
          dateRetourPrevue: { toDate: () => new Date("2024-12-05") },
          referent: "Test Référent",
          emprunteur: "Test Emprunteur",
        }),
      };

      const mockMaterielSnapshot = {
        docs: [
          {
            data: () => ({
              type: "module",
              idMateriel: "module1",
              quantite: 1,
            }),
          },
          {
            data: () => ({
              type: "stock",
              idMateriel: "stock1",
              quantite: 5,
            }),
          },
        ],
      };

      const mockModuleDoc = {
        exists: true,
        data: () => ({ nom: "Module Test" }),
      };

      const mockStockDoc = {
        exists: true,
        data: () => ({ nom: "Stock Test" }),
      };

      const mockEmpruntRef = {
        get: jest.fn().mockResolvedValue(mockEmpruntDoc),
        collection: jest.fn(() => ({
          get: jest.fn().mockResolvedValue(mockMaterielSnapshot),
        })),
      };

      mockDb.collection.mockReturnValue({
        doc: jest.fn(() => mockEmpruntRef),
      });
      mockDb.getAll
        .mockResolvedValueOnce([mockModuleDoc]) // Premier appel pour modules
        .mockResolvedValueOnce([mockStockDoc]); // Deuxième appel pour stocks

      // Act
      const result = await generateEmpruntLabels(mockData, mockContext);

      // Assert
      expect(result.success).toBe(true);
      expect(mockDb.getAll).toHaveBeenCalledTimes(2); // Une fois pour modules, une fois pour stocks
    });
  });

  describe("Erreurs de permission", () => {
    it("devrait rejeter si l'utilisateur n'est pas régisseur ou admin", async () => {
      // Arrange
      const mockData = { empruntId: "test-emprunt-id" };
      const mockContext = { auth: { uid: "test-user" } };

      mockCheckRegisseurOrAdmin.mockImplementation(() => {
        throw new functions.https.HttpsError(
          "permission-denied",
          "Accès refusé",
        );
      });

      // Act & Assert
      await expect(
        generateEmpruntLabels(mockData, mockContext),
      ).rejects.toThrow("Accès refusé");
      expect(mockCheckRegisseurOrAdmin).toHaveBeenCalledWith(mockContext);
    });
  });

  describe("Emprunt inexistant", () => {
    it("devrait rejeter si l'emprunt n'existe pas", async () => {
      // Arrange
      const mockData = { empruntId: "inexistant-id" };
      const mockContext = { auth: { uid: "test-user-id" } };

      const mockEmpruntDoc = {
        exists: false,
      };

      const mockEmpruntRef = {
        get: jest.fn().mockResolvedValue(mockEmpruntDoc),
      };

      mockDb.collection.mockReturnValue({
        doc: jest.fn(() => mockEmpruntRef),
      });

      // Act & Assert
      await expect(
        generateEmpruntLabels(mockData, mockContext),
      ).rejects.toThrow("Emprunt non trouvé");
    });

    it("devrait rejeter si l'ID d'emprunt est manquant", async () => {
      // Arrange
      const mockData = {}; // Pas d'empruntId
      const mockContext = { auth: { uid: "test-user-id" } };

      // Act & Assert
      await expect(
        generateEmpruntLabels(mockData, mockContext),
      ).rejects.toThrow("ID d'emprunt requis");
    });

    it("devrait rejeter si l'ID d'emprunt n'est pas une chaîne", async () => {
      // Arrange
      const mockData = { empruntId: 123 }; // Nombre au lieu de chaîne
      const mockContext = { auth: { uid: "test-user-id" } };

      // Act & Assert
      await expect(
        generateEmpruntLabels(mockData, mockContext),
      ).rejects.toThrow("ID d'emprunt requis");
    });
  });

  describe("Contrainte de performance 3s", () => {
    it("devrait logger un warning si la génération prend plus de 3 secondes", async () => {
      // Arrange
      const mockData = { empruntId: "test-emprunt-id" };
      const mockContext = { auth: { uid: "test-user-id" } };

      const mockEmpruntDoc = {
        exists: true,
        data: () => ({
          nom: "Test Manipulation",
          lieu: "Salle de test",
          dateDepart: { toDate: () => new Date("2024-12-01") },
          dateRetourPrevue: { toDate: () => new Date("2024-12-05") },
          referent: "Test Référent",
          emprunteur: "Test Emprunteur",
        }),
      };

      const mockMaterielSnapshot = { docs: [] };

      const mockEmpruntRef = {
        get: jest.fn().mockResolvedValue(mockEmpruntDoc),
        collection: jest.fn(() => ({
          get: jest.fn().mockResolvedValue(mockMaterielSnapshot),
        })),
      };

      mockDb.collection.mockReturnValue({
        doc: jest.fn(() => mockEmpruntRef),
      });

      // Mock performance timing pour dépasser 3 secondes
      let callCount = 0;
      jest.spyOn(Date, "now").mockImplementation(() => {
        callCount++;
        return callCount === 1 ? 1000 : 4500; // 3.5 secondes
      });

      // Act
      const result = await generateEmpruntLabels(mockData, mockContext);

      // Assert
      expect(result.duration).toBe(3500);
      expect(functions.logger.warn).toHaveBeenCalledWith(
        "Génération PDF lente: 3500ms pour test-emprunt-id",
      );
    });

    it("devrait respecter la contrainte de performance normale", async () => {
      // Arrange
      const mockData = { empruntId: "test-emprunt-id" };
      const mockContext = { auth: { uid: "test-user-id" } };

      const mockEmpruntDoc = {
        exists: true,
        data: () => ({
          nom: "Test Manipulation",
          lieu: "Salle de test",
          dateDepart: { toDate: () => new Date("2024-12-01") },
          dateRetourPrevue: { toDate: () => new Date("2024-12-05") },
          referent: "Test Référent",
          emprunteur: "Test Emprunteur",
        }),
      };

      const mockMaterielSnapshot = { docs: [] };

      const mockEmpruntRef = {
        get: jest.fn().mockResolvedValue(mockEmpruntDoc),
        collection: jest.fn(() => ({
          get: jest.fn().mockResolvedValue(mockMaterielSnapshot),
        })),
      };

      mockDb.collection.mockReturnValue({
        doc: jest.fn(() => mockEmpruntRef),
      });

      // Mock performance timing pour rester sous 3 secondes
      let callCount = 0;
      jest.spyOn(Date, "now").mockImplementation(() => {
        callCount++;
        return callCount === 1 ? 1000 : 2500; // 1.5 secondes
      });

      // Act
      const result = await generateEmpruntLabels(mockData, mockContext);

      // Assert
      expect(result.duration).toBe(1500);
      expect(functions.logger.warn).not.toHaveBeenCalled();
    });
  });

  describe("Gestion des erreurs", () => {
    it("devrait gérer les erreurs internes", async () => {
      // Arrange
      const mockData = { empruntId: "test-emprunt-id" };
      const mockContext = { auth: { uid: "test-user-id" } };

      mockDb.collection.mockImplementation(() => {
        throw new Error("Erreur Firestore");
      });

      // Act & Assert
      await expect(
        generateEmpruntLabels(mockData, mockContext),
      ).rejects.toThrow("Erreur interne lors de la génération du PDF");
      expect(functions.logger.error).toHaveBeenCalled();
    });
  });
});
