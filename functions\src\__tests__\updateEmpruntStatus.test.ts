import * as admin from "firebase-admin";
import * as functions from "firebase-functions/v1";
import { updateEmpruntStatus } from "../emprunts/updateEmpruntStatus";

// Mock Firebase Admin
jest.mock("firebase-admin", () => ({
  firestore: jest.fn(() => ({
    runTransaction: jest.fn(),
    collection: jest.fn(),
  })),
  FieldValue: {
    serverTimestamp: jest.fn(() => "TIMESTAMP"),
  },
  Timestamp: {
    fromDate: jest.fn((date) => ({ toDate: () => date })),
  },
}));

// Mock Firebase Functions
jest.mock("firebase-functions/v1", () => ({
  https: {
    HttpsError: class HttpsError extends Error {
      constructor(public code: string, public message: string) {
        super(message);
        this.name = "HttpsError";
      }
    },
    onCall: jest.fn((handler) => handler),
  },
  logger: {
    info: jest.fn(),
    error: jest.fn(),
  },
}));

// Mock auth utility
jest.mock("../utils/auth", () => ({
  checkRegisseurOrAdmin: jest.fn(),
}));

const mockDb = {
  runTransaction: jest.fn(),
  collection: jest.fn(),
};

const mockCheckRegisseurOrAdmin = require("../utils/auth").checkRegisseurOrAdmin;

describe("updateEmpruntStatus Cloud Function", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (admin.firestore as jest.Mock).mockReturnValue(mockDb);
  });

  describe("Transitions valides", () => {
    it("devrait permettre la transition de 'Pas prêt' vers 'Prêt'", async () => {
      // Arrange
      const mockData = {
        empruntId: "test-emprunt-id",
        newStatus: "Prêt",
        notes: "Test transition",
      };
      const mockContext = { auth: { uid: "test-user-id" } };

      const mockEmpruntDoc = {
        exists: true,
        data: () => ({ statut: "Pas prêt" }),
      };

      const mockEmpruntRef = {
        collection: jest.fn(() => ({
          doc: jest.fn(() => ({ set: jest.fn() })),
          get: jest.fn().mockResolvedValue({ docs: [] }), // Pas de matériel
        })),
      };

      const mockTransaction = {
        get: jest.fn()
          .mockResolvedValueOnce(mockEmpruntDoc) // Emprunt doc
          .mockResolvedValueOnce({ docs: [] }), // Matériel collection
        update: jest.fn(),
        set: jest.fn(),
        getAll: jest.fn().mockResolvedValue([]), // Pas de modules
      };

      mockDb.collection.mockReturnValue({
        doc: jest.fn(() => mockEmpruntRef),
      });

      mockDb.runTransaction.mockImplementation(async (callback) => {
        return await callback(mockTransaction);
      });

      // Act
      const result = await updateEmpruntStatus(mockData, mockContext);

      // Assert
      expect(mockCheckRegisseurOrAdmin).toHaveBeenCalledWith(mockContext);
      expect(mockDb.runTransaction).toHaveBeenCalled();
      expect(result).toEqual({
        success: true,
        result: {
          id: "test-emprunt-id",
          oldStatus: "Pas prêt",
          newStatus: "Prêt",
        },
      });
    });

    it("devrait permettre la transition de 'Prêt' vers 'Parti'", async () => {
      // Arrange
      const mockData = {
        empruntId: "test-emprunt-id",
        newStatus: "Parti",
      };
      const mockContext = { auth: { uid: "test-user-id" } };

      const mockEmpruntDoc = {
        exists: true,
        data: () => ({ statut: "Prêt" }),
      };

      const mockMaterielSnapshot = {
        docs: [
          {
            data: () => ({
              type: "module",
              idMateriel: "module1",
            }),
          },
        ],
      };

      const mockEmpruntRef = {
        collection: jest.fn(() => ({
          doc: jest.fn(() => ({ set: jest.fn() })),
        })),
      };

      const mockTransaction = {
        get: jest.fn()
          .mockResolvedValueOnce(mockEmpruntDoc)
          .mockResolvedValueOnce(mockMaterielSnapshot),
        update: jest.fn(),
        set: jest.fn(),
      };

      mockDb.collection.mockReturnValue({
        doc: jest.fn(() => mockEmpruntRef),
      });

      mockDb.runTransaction.mockImplementation(async (callback) => {
        return await callback(mockTransaction);
      });

      // Act
      const result = await updateEmpruntStatus(mockData, mockContext);

      // Assert
      expect(result.result.newStatus).toBe("Parti");
      expect(mockTransaction.update).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({
          estPret: false,
        }),
      );
    });

    it("devrait permettre la transition de 'Parti' vers 'Revenu'", async () => {
      // Arrange
      const mockData = {
        empruntId: "test-emprunt-id",
        newStatus: "Revenu",
        dateRetourEffective: new Date("2024-12-05"),
      };
      const mockContext = { auth: { uid: "test-user-id" } };

      const mockEmpruntDoc = {
        exists: true,
        data: () => ({ statut: "Parti" }),
      };

      const mockTransaction = {
        get: jest.fn()
          .mockResolvedValueOnce(mockEmpruntDoc)
          .mockResolvedValueOnce({ docs: [] }),
        update: jest.fn(),
        set: jest.fn(),
      };

      mockDb.runTransaction.mockImplementation(async (callback) => {
        return await callback(mockTransaction);
      });

      // Act
      const result = await updateEmpruntStatus(mockData, mockContext);

      // Assert
      expect(result.result.newStatus).toBe("Revenu");
    });

    it("devrait permettre la transition de 'Revenu' vers 'Inventorié'", async () => {
      // Arrange
      const mockData = {
        empruntId: "test-emprunt-id",
        newStatus: "Inventorié",
      };
      const mockContext = { auth: { uid: "test-user-id" } };

      const mockEmpruntDoc = {
        exists: true,
        data: () => ({ statut: "Revenu" }),
      };

      const mockMaterielSnapshot = {
        docs: [
          {
            ref: { update: jest.fn() },
            data: () => ({ type: "module" }),
          },
        ],
      };

      const mockTransaction = {
        get: jest.fn()
          .mockResolvedValueOnce(mockEmpruntDoc)
          .mockResolvedValueOnce(mockMaterielSnapshot),
        update: jest.fn(),
        set: jest.fn(),
      };

      mockDb.runTransaction.mockImplementation(async (callback) => {
        return await callback(mockTransaction);
      });

      // Act
      const result = await updateEmpruntStatus(mockData, mockContext);

      // Assert
      expect(result.result.newStatus).toBe("Inventorié");
    });
  });

  describe("Transitions interdites", () => {
    it("devrait rejeter une transition non autorisée", async () => {
      // Arrange
      const mockData = {
        empruntId: "test-emprunt-id",
        newStatus: "Parti", // Transition directe de "Pas prêt" vers "Parti" interdite
      };
      const mockContext = { auth: { uid: "test-user-id" } };

      const mockEmpruntDoc = {
        exists: true,
        data: () => ({ statut: "Pas prêt" }),
      };

      const mockTransaction = {
        get: jest.fn().mockResolvedValue(mockEmpruntDoc),
      };

      mockDb.collection.mockReturnValue({
        doc: jest.fn(),
      });

      mockDb.runTransaction.mockImplementation(async (callback) => {
        return await callback(mockTransaction);
      });

      // Act & Assert
      await expect(updateEmpruntStatus(mockData, mockContext)).rejects.toThrow(
        'Transition non autorisée de "Pas prêt" vers "Parti"',
      );
    });

    it("devrait rejeter une transition depuis 'Inventorié'", async () => {
      // Arrange
      const mockData = {
        empruntId: "test-emprunt-id",
        newStatus: "Prêt",
      };
      const mockContext = { auth: { uid: "test-user-id" } };

      const mockEmpruntDoc = {
        exists: true,
        data: () => ({ statut: "Inventorié" }),
      };

      const mockTransaction = {
        get: jest.fn().mockResolvedValue(mockEmpruntDoc),
      };

      mockDb.runTransaction.mockImplementation(async (callback) => {
        return await callback(mockTransaction);
      });

      // Act & Assert
      await expect(updateEmpruntStatus(mockData, mockContext)).rejects.toThrow(
        'Transition non autorisée de "Inventorié" vers "Prêt"',
      );
    });
  });

  describe("Erreurs de permission", () => {
    it("devrait rejeter si l'utilisateur n'est pas régisseur ou admin", async () => {
      // Arrange
      const mockData = {
        empruntId: "test-emprunt-id",
        newStatus: "Prêt",
      };
      const mockContext = { auth: { uid: "test-user" } };

      mockCheckRegisseurOrAdmin.mockImplementation(() => {
        throw new functions.https.HttpsError(
          "permission-denied",
          "Accès refusé",
        );
      });

      // Act & Assert
      await expect(updateEmpruntStatus(mockData, mockContext)).rejects.toThrow(
        "Accès refusé",
      );
      expect(mockCheckRegisseurOrAdmin).toHaveBeenCalledWith(mockContext);
    });
  });

  describe("Gestion des statuts spécifiques", () => {
    it("devrait vérifier que les modules sont prêts pour le statut 'Prêt'", async () => {
      // Arrange
      const mockData = {
        empruntId: "test-emprunt-id",
        newStatus: "Prêt",
      };
      const mockContext = { auth: { uid: "test-user-id" } };

      const mockEmpruntDoc = {
        exists: true,
        data: () => ({ statut: "Pas prêt" }),
      };

      const mockMaterielSnapshot = {
        docs: [
          {
            data: () => ({
              type: "module",
              idMateriel: "module1",
            }),
          },
        ],
      };

      const mockModuleDoc = {
        exists: true,
        data: () => ({ estPret: false }), // Module non prêt
      };

      const mockTransaction = {
        get: jest.fn()
          .mockResolvedValueOnce(mockEmpruntDoc)
          .mockResolvedValueOnce(mockMaterielSnapshot),
        getAll: jest.fn().mockResolvedValue([mockModuleDoc]),
      };

      mockDb.runTransaction.mockImplementation(async (callback) => {
        return await callback(mockTransaction);
      });

      // Act & Assert
      await expect(updateEmpruntStatus(mockData, mockContext)).rejects.toThrow(
        "Module non prêt: module1",
      );
    });

    it("devrait marquer les modules comme non disponibles pour le statut 'Parti'", async () => {
      // Arrange
      const mockData = {
        empruntId: "test-emprunt-id",
        newStatus: "Parti",
      };
      const mockContext = { auth: { uid: "test-user-id" } };

      const mockEmpruntDoc = {
        exists: true,
        data: () => ({ statut: "Prêt" }),
      };

      const mockMaterielSnapshot = {
        docs: [
          {
            data: () => ({
              type: "module",
              idMateriel: "module1",
            }),
          },
        ],
      };

      const mockTransaction = {
        get: jest.fn()
          .mockResolvedValueOnce(mockEmpruntDoc)
          .mockResolvedValueOnce(mockMaterielSnapshot),
        update: jest.fn(),
        set: jest.fn(),
      };

      mockDb.collection.mockReturnValue({
        doc: jest.fn(),
      });

      mockDb.runTransaction.mockImplementation(async (callback) => {
        return await callback(mockTransaction);
      });

      // Act
      await updateEmpruntStatus(mockData, mockContext);

      // Assert
      expect(mockTransaction.update).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({
          estPret: false,
        }),
      );
    });

    it("devrait marquer le matériel comme complet pour le statut 'Inventorié'", async () => {
      // Arrange
      const mockData = {
        empruntId: "test-emprunt-id",
        newStatus: "Inventorié",
      };
      const mockContext = { auth: { uid: "test-user-id" } };

      const mockEmpruntDoc = {
        exists: true,
        data: () => ({ statut: "Revenu" }),
      };

      const mockMaterielDoc = {
        ref: { update: jest.fn() },
        data: () => ({ type: "module" }),
      };

      const mockMaterielSnapshot = {
        docs: [mockMaterielDoc],
      };

      const mockTransaction = {
        get: jest.fn()
          .mockResolvedValueOnce(mockEmpruntDoc)
          .mockResolvedValueOnce(mockMaterielSnapshot),
        update: jest.fn(),
        set: jest.fn(),
      };

      mockDb.runTransaction.mockImplementation(async (callback) => {
        return await callback(mockTransaction);
      });

      // Act
      await updateEmpruntStatus(mockData, mockContext);

      // Assert
      expect(mockTransaction.update).toHaveBeenCalledWith(
        mockMaterielDoc.ref,
        expect.objectContaining({
          estComplet: true,
        }),
      );
    });
  });

  describe("Validation des données", () => {
    it("devrait rejeter si l'emprunt n'existe pas", async () => {
      // Arrange
      const mockData = {
        empruntId: "inexistant-id",
        newStatus: "Prêt",
      };
      const mockContext = { auth: { uid: "test-user-id" } };

      const mockEmpruntDoc = {
        exists: false,
      };

      const mockTransaction = {
        get: jest.fn().mockResolvedValue(mockEmpruntDoc),
      };

      mockDb.runTransaction.mockImplementation(async (callback) => {
        return await callback(mockTransaction);
      });

      // Act & Assert
      await expect(updateEmpruntStatus(mockData, mockContext)).rejects.toThrow(
        "Emprunt non trouvé",
      );
    });

    it("devrait rejeter si l'ID d'emprunt est manquant", async () => {
      // Arrange
      const mockData = { newStatus: "Prêt" }; // Pas d'empruntId
      const mockContext = { auth: { uid: "test-user-id" } };

      // Act & Assert
      await expect(updateEmpruntStatus(mockData, mockContext)).rejects.toThrow(
        "ID d'emprunt requis",
      );
    });

    it("devrait rejeter si le nouveau statut est invalide", async () => {
      // Arrange
      const mockData = {
        empruntId: "test-emprunt-id",
        newStatus: "StatutInvalide",
      };
      const mockContext = { auth: { uid: "test-user-id" } };

      // Act & Assert
      await expect(updateEmpruntStatus(mockData, mockContext)).rejects.toThrow(
        "Statut invalide",
      );
    });

    it("devrait rejeter si les données sont nulles", async () => {
      // Arrange
      const mockData = null;
      const mockContext = { auth: { uid: "test-user-id" } };

      // Act & Assert
      await expect(updateEmpruntStatus(mockData, mockContext)).rejects.toThrow(
        "Données invalides",
      );
    });
  });

  describe("Gestion des erreurs", () => {
    it("devrait gérer les erreurs de transaction Firestore", async () => {
      // Arrange
      const mockData = {
        empruntId: "test-emprunt-id",
        newStatus: "Prêt",
      };
      const mockContext = { auth: { uid: "test-user-id" } };

      mockDb.runTransaction.mockRejectedValue(new Error("Transaction failed"));

      // Act & Assert
      await expect(updateEmpruntStatus(mockData, mockContext)).rejects.toThrow(
        "Erreur interne lors de la mise à jour du statut",
      );
    });
  });
});
